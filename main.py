from pack import *
import devbot
import danbot
import random


class Game():
	def __init__(self, players) -> None:
		self.drawPile: list[CARD] = DECK().create_deck() * 2
		self.playPile: list[CARD] = []
		self.game_finished: bool = False
		self.players: list[danbot.BOT] = players

	def checkCards(self, hand: list[PURE_SEQUENCE | IMPURE_SEQUENCE | SET], wildCard: CARD) -> bool:
		pureSequences : list[PURE_SEQUENCE] = [run for run in hand if isinstance(run, PURE_SEQUENCE)]
		impureSequences : list[IMPURE_SEQUENCE] = [run for run in hand if isinstance(run, IMPURE_SEQUENCE)]
		sets : list[SET] = [run for run in hand if isinstance(run, SET)]

		if (
			len(hand) >= 2 and
			len(pureSequences) >= 1 and
			(not len(sets) >= 1 or len(impureSequences) >= 1) and
			(not len(pureSequences) == 1 or len(impureSequences) >= 1) and
			not any(len(run.cards) < 3 for run in hand) and
			not any(not run.is_valid(wildCard) for run in hand)
		):
			return True
		else:
			return False

	def begin(self) -> None:
		print("Let's begin! \n\n")

		print("Shuffling cards...")
		random.shuffle(self.drawPile)

		print("Dealing cards...\n")
		for player in self.players:
			player.hand += [self.drawPile.pop() for _ in range(13)]
			print(f"{player.name}'s hand: {player.hand}\n")

		wildCard: CARD = self.drawPile.pop()
		print(f"Wildcard is: {wildCard}\n")

		self.playPile.append(self.drawPile.pop())
		topCard: CARD = self.playPile[-1]
		print(f"Top card is: {topCard}\n")

		while not self.game_finished:
			for player in self.players:
				turnFinished: bool = False
				while not turnFinished:
					match player.nextMove(topCard):
						case "pick":
							player.hand.append(self.playPile.pop())
							self.playPile.append(player.takeCard())
							turnFinished = True
						case "draw":
							player.hand.append(self.drawPile.pop())
							self.playPile.append(player.takeCard())
							turnFinished = True
						case "check":
							if self.checkCards(player.checkRummy(), wildCard):
								self.game_finished = True
								turnFinished = True
								print(f"Congratulations! \n{player.name} wins!")
							else:
								print("Rummy has not been achieved. The game shall continue.")

				if self.game_finished:
					break

				topCard = self.playPile[-1]
				print(f"Top card is: {topCard}\n")


def main() -> None:
	print("===== Welcome to Indian Rummy! =====\n")
	player1: danbot.BOT = danbot.BOT("Dhanush")
	player2: danbot.BOT = danbot.BOT("Devjith")
	game: Game = Game([player1, player2])
	
	while True:
		match input("Select an option: \n1. Begin Game \n2. Exit \n--> "):
			case "1":
				game.begin()
			case "2":
				exit()
			case _:
				print("Invalid option. Please try again.")


if __name__ == "__main__":
	main()
